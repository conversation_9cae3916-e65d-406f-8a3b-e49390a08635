{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "node.js prometheus client basic metrics", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 11159, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 10, "x": 0, "y": 0}, "id": 6, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "irate(process_cpu_user_seconds_total{instance=~\"$instance\"}[2m]) * 100", "format": "time_series", "intervalFactor": 1, "legendFormat": "User CPU - {{instance}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "irate(process_cpu_system_seconds_total{instance=~\"$instance\"}[2m]) * 100", "format": "time_series", "intervalFactor": 1, "legendFormat": "Sys CPU - {{instance}}", "refId": "B"}], "title": "Process CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 10, "y": 0}, "id": 8, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_eventloop_lag_seconds{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "title": "Event Loop Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 5, "x": 19, "y": 0}, "id": 2, "interval": "", "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "/^v22\\.3\\.0$/", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "11.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(nodejs_version_info{instance=~\"$instance\"}) by (version)", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Node.js Version", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "#F2495C", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 5, "x": 19, "y": 3}, "id": 4, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "sum(changes(process_start_time_seconds{instance=~\"$instance\"}[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "title": "Process Restart Times", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 16, "x": 0, "y": 7}, "id": 7, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "process_resident_memory_bytes{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Process Memory - {{instance}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_heap_size_total_bytes{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Heap Total - {{instance}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_heap_size_used_bytes{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Heap Used - {{instance}}", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_external_memory_bytes{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "External Memory - {{instance}}", "refId": "D"}], "title": "Process Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 7}, "id": 9, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_active_handles_total{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Active Handler - {{instance}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_active_requests_total{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Active Request - {{instance}}", "refId": "B"}], "title": "Active Handlers/Requests Total", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 14}, "id": 10, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_heap_space_size_total_bytes{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Heap Total - {{instance}} - {{space}}", "refId": "A"}], "title": "Heap Total Detail", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 14}, "id": 11, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_heap_space_size_used_bytes{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Heap Used - {{instance}} - {{space}}", "refId": "A"}], "title": "Heap Used Detail", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 14}, "id": 12, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "nodejs_heap_space_size_available_bytes{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Heap Used - {{instance}} - {{space}}", "refId": "A"}], "title": "Heap Available Detail", "type": "timeseries"}], "schemaVersion": 39, "tags": ["nodejs"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "definition": "label_values(nodejs_version_info, instance)", "hide": 0, "includeAll": true, "label": "instance", "multi": true, "name": "instance", "options": [], "query": "label_values(nodejs_version_info, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "FlowiseAI Server", "uid": "PTSqcpJWk", "version": 5, "weekStart": ""}