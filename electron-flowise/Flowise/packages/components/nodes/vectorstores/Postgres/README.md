# Postgres Vector Store

Postgres Vector Store integration for Flowise

## 🌱 Env Variables

| Variable                                 | Description                                           | Type    | Default     |
| ---------------------------------------- | ----------------------------------------------------- | ------- | ----------- |
| POSTGRES_VECTORSTORE_HOST                | Default `host` for Postgres Vector Store              | String  |             |
| POSTGRES_VECTORSTORE_PORT                | Default `port` for Postgres Vector Store              | Number  | 5432        |
| POSTGRES_VECTORSTORE_USER                | Default `user` for Postgres Vector Store              | String  |             |
| POSTGRES_VECTORSTORE_PASSWORD            | Default `password` for Postgres Vector Store          | String  |             |
| POSTGRES_VECTORSTORE_DATABASE            | Default `database` for Postgres Vector Store          | String  |             |
| POSTGRES_VECTORSTORE_TABLE_NAME          | Default `tableName` for Postgres Vector Store         | String  | documents   |
| POSTGRES_VECTORSTORE_CONTENT_COLUMN_NAME | Default `contentColumnName` for Postgres Vector Store | String  | pageContent |
| POSTGRES_VECTORSTORE_SSL                 | Default `ssl` for Postgres Vector Store               | Boolean | false       |

## License

Source code in this repository is made available under the [Apache License Version 2.0](https://github.com/FlowiseAI/Flowise/blob/master/LICENSE.md).
