import { INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses, stripHTMLFromToolInput } from '../../../src/utils'
import { RequestParameters, desc, RequestsPostTool } from './core'

const codeExample = `{
    "name": {
        "type": "string",
        "required": true,
        "description": "Name of the item"
    },
    "date": {
        "type": "string",
        "description": "Date of the item"
    }
}`

class RequestsPost_Tools implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    category: string
    baseClasses: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'Requests Post'
        this.name = 'requestsPost'
        this.version = 2.0
        this.type = 'RequestsPost'
        this.icon = 'post.png'
        this.category = 'Tools'
        this.description = 'Execute HTTP POST requests'
        this.baseClasses = [this.type, ...getBaseClasses(RequestsPostTool)]
        this.inputs = [
            {
                label: 'URL',
                name: 'requestsPostUrl',
                type: 'string',
                acceptVariable: true
            },
            {
                label: 'Name',
                name: 'requestsPostName',
                type: 'string',
                default: 'requests_post',
                description: 'Name of the tool',
                additionalParams: true,
                optional: true
            },
            {
                label: 'Description',
                name: 'requestsPostDescription',
                type: 'string',
                rows: 4,
                default: desc,
                description: 'Describe to LLM when it should use this tool',
                additionalParams: true,
                optional: true
            },
            {
                label: 'Headers',
                name: 'requestsPostHeaders',
                type: 'string',
                rows: 4,
                acceptVariable: true,
                additionalParams: true,
                optional: true,
                placeholder: `{
    "Authorization": "Bearer <token>"
}`
            },
            {
                label: 'Body',
                name: 'requestPostBody',
                type: 'string',
                rows: 4,
                description: 'JSON body for the POST request. This will override the body generated by the LLM',
                additionalParams: true,
                acceptVariable: true,
                optional: true,
                placeholder: `{
    "name": "John Doe",
    "age": 30
}`
            },
            {
                label: 'Body Schema',
                name: 'requestsPostBodySchema',
                type: 'code',
                description: 'Description of the available body params to enable LLM to figure out which body params to use',
                placeholder: `{
    "name": {
        "type": "string",
        "required": true,
        "description": "Name of the item"
    },
    "date": {
        "type": "string",
        "description": "Date of the item"
    }
}`,
                optional: true,
                hideCodeExecute: true,
                additionalParams: true,
                codeExample: codeExample
            },
            {
                label: 'Max Output Length',
                name: 'requestsPostMaxOutputLength',
                type: 'number',
                description: 'Max length of the output. Remove this if you want to return the entire response',
                default: '2000',
                step: 1,
                optional: true,
                additionalParams: true
            }
        ]
    }

    async init(nodeData: INodeData): Promise<any> {
        const headers = (nodeData.inputs?.headers as string) || (nodeData.inputs?.requestsPostHeaders as string)
        const url = (nodeData.inputs?.url as string) || (nodeData.inputs?.requestsPostUrl as string)
        const name = (nodeData.inputs?.name as string) || (nodeData.inputs?.requestsPostName as string)
        const description = (nodeData.inputs?.description as string) || (nodeData.inputs?.requestsPostDescription as string)
        const body = (nodeData.inputs?.body as string) || (nodeData.inputs?.requestPostBody as string)
        const bodySchema = nodeData.inputs?.requestsPostBodySchema as string
        const maxOutputLength = (nodeData.inputs?.maxOutputLength as string) || (nodeData.inputs?.requestsPostMaxOutputLength as string)

        const obj: RequestParameters = {}
        if (url) obj.url = stripHTMLFromToolInput(url)
        if (description) obj.description = description
        if (name)
            obj.name = name
                .toLowerCase()
                .replace(/ /g, '_')
                .replace(/[^a-z0-9_-]/g, '')
        if (bodySchema) obj.bodySchema = stripHTMLFromToolInput(bodySchema)
        if (maxOutputLength) obj.maxOutputLength = parseInt(maxOutputLength, 10)
        if (headers) {
            const parsedHeaders = typeof headers === 'object' ? headers : JSON.parse(stripHTMLFromToolInput(headers))
            obj.headers = parsedHeaders
        }
        if (body) {
            const parsedBody = typeof body === 'object' ? body : JSON.parse(body)
            obj.body = parsedBody
        }

        return new RequestsPostTool(obj)
    }
}

module.exports = { nodeClass: RequestsPost_Tools }
