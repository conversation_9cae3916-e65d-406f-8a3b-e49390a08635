import React, { useState, useEffect } from 'react'
import { Routes, Route, useNavigate } from 'react-router-dom'
import Loading from './Loading'
import MainApp from './MainApp'

const App = () => {
  const [isServerReady, setIsServerReady] = useState(false)
  const navigate = useNavigate()

  // Function to check if localhost:3000 is available
  const checkServerAvailability = async () => {
    try {
      const response = await fetch('http://localhost:3000', {
        method: 'HEAD',
        mode: 'no-cors'
      })
      return true
    } catch (error) {
      return false
    }
  }

  useEffect(() => {
    let interval

    const startChecking = async () => {
      // Check immediately
      const isAvailable = await checkServerAvailability()
      if (isAvailable) {
        setIsServerReady(true)
        navigate('/app')
        return
      }

      // If not available, start interval checking
      interval = setInterval(async () => {
        const isAvailable = await checkServerAvailability()
        if (isAvailable) {
          setIsServerReady(true)
          navigate('/app')
          clearInterval(interval)
        }
      }, 2000) // Check every 2 seconds
    }

    startChecking()

    // Cleanup interval on component unmount
    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [navigate])

  return (
    <Routes>
      <Route path="/" element={<Loading />} />
      <Route path="/app" element={<MainApp />} />
    </Routes>
  )
}

export default App
