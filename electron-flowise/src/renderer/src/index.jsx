import React from 'react'
import { createRoot } from 'react-dom/client'

// third party
import { HashRouter } from 'react-router-dom'

// local imports
import App from './components/App'
import './styles/global.css'

const container = document.getElementById('root')
const root = createRoot(container)

root.render(
    <React.StrictMode>
        <HashRouter>
            <App />
        </HashRouter>
    </React.StrictMode>
)
