import React from 'react'
import { createRoot } from 'react-dom/client'

// third party
import { HashRouter } from 'react-router-dom'
import { Provider } from 'react-redux'

const container = document.getElementById('root')
const root = createRoot(container)

root.render(
    <React.StrictMode>
        <Provider store={store}>
            <HashRouter>
                Loading...
            </HashRouter>
        </Provider>
    </React.StrictMode>
)
